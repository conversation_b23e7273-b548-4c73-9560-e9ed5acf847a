# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-02 16:33:01 - Log of updates made.

*
      
## Decision

*   [2025-07-02 16:34:01] - The project will be a C# Console Application.

## Rationale

*   The requirements specify a command-line tool, making a console application the most direct and suitable project type. It avoids unnecessary overhead from GUI frameworks.

## Implementation Details

*   The project will be created using `dotnet new console`. The main entry point will be in `Program.cs`.

---
### Decision
[2025-07-02 16:34:01] - Use the `ImageSharp` library for visualization.

**Rationale:**
`ImageSharp` is a modern, cross-platform, and fully managed .NET library for image processing. It has no native dependencies, making it easy to deploy across different operating systems (Windows, macOS, Linux), which aligns with the potential need for a versatile tool. It is also actively maintained and feature-rich. `SkiaSharp` is a powerful alternative but introduces native dependencies, which can complicate deployment.

**Implications/Details:**
The `SixLabors.ImageSharp` NuGet package will be added to the project. The `Visualizer.cs` class will encapsulate all drawing logic using APIs from this library.

---
### Decision (Code)
[2025-07-02 16:37:17] - Added `SixLabors.ImageSharp.Drawing` NuGet package alongside `SixLabors.ImageSharp`.

**Rationale:**
The architecture document only specified `SixLabors.ImageSharp`. However, to draw shapes, lines, and points for the visualization as required by the `Visualizer.cs` component, the `SixLabors.ImageSharp.Drawing` extension library is necessary. This is a minor addition that enables the core visualization functionality.

**Details:**
The package was added to the `PointCloudSegmentation.csproj` file.

---
### Decision (Code)
[2025-07-02 16:47:37] - Adjusted X-axis partitioning logic based on user feedback.

**Rationale:**
The user reported that the X-axis partitioning was incorrect. The original implementation used a `1/3`, `1/3`, `1/3` split. The user requested a `1/6`, `1/2` (`3/6`), `1/6` split. This change aligns the application's behavior with the user's requirements.

**Details:**
- Modified `PointCloudProcessor.cs`: Updated the `PartitionPoints` method to calculate split points at `width * 1/6` and `width * 4/6`.
- Modified `Visualizer.cs`: Updated the `GenerateVisualization` method to draw partition lines corresponding to the new logic.

---
### Decision (Code)
[2025-07-02 16:56:00] - Updated visualization and region naming based on new requirements.

**Rationale:**
The user requested changes to the visualization and data output. The region names were simplified to numeric IDs (`Region_1`, `Region_2`, etc.) for easier parsing and less descriptive naming. The visualization was updated to draw the bounding box of the entire point cloud and label regions with their numeric ID.

**Details:**
- **`PointCloudProcessor.cs`**: Modified `PartitionPoints` to use numeric region names (e.g., "Region_1").
- **`Visualizer.cs`**: 
    - Added logic to draw the main bounding box using a black pen.
    - Updated the color map to use the new region names.
    - Added logic to extract the numeric ID from the region name and draw it at the center of the region.
- **`PointCloudModels.cs`**: Added `CenterX` and `CenterY` properties to the `BoundingBox` class to facilitate label placement.

---
### Decision (Code)
[2025-07-02 17:04:24] - Fixed Y-axis inversion and added corner coordinate labels in visualization.

**Rationale:**
User feedback indicated that the Y-axis in the generated visualization was inverted, causing data with higher Y values to appear at the bottom. Additionally, to improve clarity, the user requested that the (X, Y) coordinates of the bounding box's four corners be displayed on the image.

**Details:**
- **`PointCloudSegmentation/Visualizer.cs`**:
    - Modified the Y-coordinate calculation for all drawing operations (points, labels, lines) to use the correct inversion formula: `py = (imageHeight - padding) - (worldY - bbox.MinY) * scale`. This ensures the visualization matches a standard Cartesian coordinate system.
    - Added logic to calculate the screen positions of the bounding box's four corners (`minX, minY`, `maxX, minY`, `minX, maxY`, `maxX, maxY`).
    - Used `DrawText` to render the formatted (two decimal places) coordinates of each corner on the final PNG image.