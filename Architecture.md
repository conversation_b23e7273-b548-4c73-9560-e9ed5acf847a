# C# 点云分割工具 - 架构设计

本文档定义了 C# 点云分割工具的项目架构。该设计基于提供的规范和伪代码，旨在创建一个模块化、可扩展且易于维护的控制台应用程序。

## 1. 项目结构

该项目将被构建为一个标准的 C# 控制台应用程序。文件结构如下：

```
PointCloudSegmentation/
├── PointCloudSegmentation.csproj
├── Program.cs
├── PointCloudModels.cs
├── PointCloudProcessor.cs
├── Visualizer.cs
└── output_data/
    ├── (生成的 .txt 和 .png 文件)
```

- **`PointCloudSegmentation.csproj`**: .NET 项目文件，定义项目元数据和依赖项。
- **`Program.cs`**: 应用程序的主入口点，负责协调整个流程。
- **`PointCloudModels.cs`**: 包含核心数据结构，如 `Point3D`、`BoundingBox` 和 `Region`。
- **`PointCloudProcessor.cs`**: 包含核心业务逻辑，如文件读取、边界计算和点分区。
- **`Visualizer.cs`**: 负责使用 `ImageSharp` 库生成 PNG 可视化图像。
- **`output_data/`**: 程序运行时自动创建的目录，用于存放所有输出文件。

## 2. 依赖项

项目将依赖以下 NuGet 包：

- **`SixLabors.ImageSharp`**: 一个强大的、跨平台的 2D 图形库，用于生成 `partition_visualization.png`。它将通过 NuGet 包管理器添加到项目中。

  ```powershell
  dotnet add package SixLabors.ImageSharp
  ```

## 3. 类和结构设计

### 3.1. `PointCloudModels.cs`

此文件定义了应用程序中使用的数据模型。

```csharp
// PointCloudModels.cs
namespace PointCloudSegmentation
{
    /// <summary>
    /// 表示一个三维空间中的点。
    /// </summary>
    public struct Point3D
    {
        public double X { get; set; }
        public double Y { get; set; }
        public double Z { get; set; }
    }

    /// <summary>
    /// 定义点云在 X-Y 平面上的二维最小外接矩形。
    /// </summary>
    public class BoundingBox
    {
        public double MinX { get; set; }
        public double MaxX { get; set; }
        public double MinY { get; set; }
        public double MaxY { get; set; }
        public double Width => MaxX - MinX;
        public double Height => MaxY - MinY;
    }

    /// <summary>
    /// 表示一个地理分区，包含其名称、边界和点集。
    /// </summary>
    public class Region
    {
        public string Name { get; set; }
        public List<Point3D> Points { get; } = new List<Point3D>();
    }
}
```

### 3.2. `PointCloudProcessor.cs`

此文件包含处理点云的核心逻辑。

```csharp
// PointCloudProcessor.cs
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace PointCloudSegmentation
{
    /// <summary>
    /// 负责处理点云数据，包括加载、分区和文件写入。
    /// </summary>
    public static class PointCloudProcessor
    {
        /// <summary>
        /// 从指定的 .txt 文件中读取点云数据。
        /// </summary>
        public static List<Point3D> ReadPointCloudFile(string filePath)
        {
            // 实现：读取文件，解析每一行，处理异常
            return new List<Point3D>();
        }

        /// <summary>
        /// 计算点云的二维边界框。
        /// </summary>
        public static BoundingBox CalculateBoundingBox(List<Point3D> points)
        {
            // 实现：遍历点集，找到 MinX, MaxX, MinY, MaxY
            return new BoundingBox();
        }

        /// <summary>
        /// 将点分配到六个预定义的区域。
        /// </summary>
        public static Dictionary<string, Region> PartitionPoints(List<Point3D> points, BoundingBox bbox)
        {
            // 实现：定义6个区域，遍历所有点并将其分配到正确的区域
            return new Dictionary<string, Region>();
        }

        /// <summary>
        /// 将分区后的数据写入单独的 .txt 文件。
        /// </summary>
        public static void WriteRegionFiles(Dictionary<string, Region> regions, string outputDir)
        {
            // 实现：为每个非空区域创建一个文件并写入点数据
        }
    }
}
```

### 3.3. `Visualizer.cs`

此文件封装了所有与图像生成相关的逻辑。

```csharp
// Visualizer.cs
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.PixelFormats;
using System.Collections.Generic;

namespace PointCloudSegmentation
{
    /// <summary>
    /// 负责生成点云分区的可视化图像。
    /// </summary>
    public static class Visualizer
    {
        /// <summary>
        /// 生成并保存一个 PNG 图像，显示分区和点。
        /// </summary>
        public static void GenerateVisualization(Dictionary<string, Region> regions, BoundingBox bbox, string outputPath)
        {
            // 实现：
            // 1. 创建一个 Image<Rgba32> 画布。
            // 2. 定义颜色映射。
            // 3. 绘制分区边界线。
            // 4. 将每个点绘制为画布上的一个像素或小圆圈。
            // 5. 保存图像到 outputPath。
        }
    }
}
```

### 3.4. `Program.cs`

应用程序的主入口点，负责编排整个工作流程。

```csharp
// Program.cs
using System;
using System.IO;

namespace PointCloudSegmentation
{
    class Program
    {
        static void Main(string[] args)
        {
            // 1. 验证命令行参数
            if (args.Length == 0)
            {
                Console.WriteLine("错误：请输入点云文件的路径。");
                return;
            }
            string filePath = args[0];

            // 2. 设置输出目录
            string outputDir = "output_data";
            Directory.CreateDirectory(outputDir);

            try
            {
                // 3. 加载数据
                var points = PointCloudProcessor.ReadPointCloudFile(filePath);
                if (points.Count == 0)
                {
                    Console.WriteLine("警告：文件中没有找到有效的数据点。");
                    return;
                }

                // 4. 计算边界
                var bbox = PointCloudProcessor.CalculateBoundingBox(points);

                // 5. 分区
                var regions = PointCloudProcessor.PartitionPoints(points, bbox);

                // 6. 写入数据文件
                PointCloudProcessor.WriteRegionFiles(regions, outputDir);

                // 7. 生成可视化图像
                string imagePath = Path.Combine(outputDir, "partition_visualization.png");
                Visualizer.GenerateVisualization(regions, bbox, imagePath);

                Console.WriteLine($"处理完成。输出文件已保存到 '{outputDir}' 目录。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }
        }
    }
}
```

## 4. 总结

该架构将应用程序的功能清晰地分离到不同的类中：
- **模型 (`PointCloudModels.cs`)**: 定义数据结构。
- **逻辑 (`PointCloudProcessor.cs`)**: 执行核心计算和文件操作。
- **可视化 (`Visualizer.cs`)**: 处理图像生成。
- **入口点 (`Program.cs`)**: 协调所有操作。

这种设计遵循了单一职责原则，使得代码更易于理解、测试和扩展。下一步是基于这些骨架文件实现具体的处理逻辑。