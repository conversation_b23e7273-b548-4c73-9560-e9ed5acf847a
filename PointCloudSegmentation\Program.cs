// Program.cs
using System;
using System.IO;

namespace PointCloudSegmentation
{
    class Program
    {
        static void Main(string[] args)
        {
            // 1. 验证命令行参数
            if (args.Length == 0)
            {
                Console.WriteLine("错误：请输入点云文件的路径。");
                Console.WriteLine("用法: dotnet run -- <path_to_your_point_cloud_file.txt>");
                return;
            }
            string filePath = args[0];

            // 2. 设置输出目录
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string inputFileName = Path.GetFileNameWithoutExtension(filePath);
            string outputDir = Path.Combine("output_data", $"{timestamp}_{inputFileName}");
            
            try
            {
                // 3. 加载数据
                Console.WriteLine("正在加载点云数据...");
                var points = PointCloudProcessor.ReadPointCloudFile(filePath);
                if (points.Count == 0)
                {
                    Console.WriteLine("警告：文件中没有找到有效的数据点。");
                    return;
                }
                Console.WriteLine($"成功加载 {points.Count} 个点。");

                // 4. 计算边界
                Console.WriteLine("正在计算边界框...");
                var bbox = PointCloudProcessor.CalculateBoundingBox(points);
                Console.WriteLine($"边界框: MinX={bbox.MinX}, MaxX={bbox.MaxX}, MinY={bbox.MinY}, MaxY={bbox.MaxY}");

                // 5. 分区
                Console.WriteLine("正在将点分区...");
                var regions = PointCloudProcessor.PartitionPoints(points, bbox);

                // 6. 写入数据文件
                Console.WriteLine("正在写入区域文件...");
                PointCloudProcessor.WriteRegionFiles(regions, outputDir);

                // 7. 生成可视化图像
                Console.WriteLine("正在生成可视化图像...");
                string imagePath = Path.Combine(outputDir, "partition_visualization.png");
                Visualizer.GenerateVisualization(regions, bbox, imagePath);

                Console.WriteLine($"处理完成。输出文件已保存到 '{outputDir}' 目录。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }
        }
    }
}
