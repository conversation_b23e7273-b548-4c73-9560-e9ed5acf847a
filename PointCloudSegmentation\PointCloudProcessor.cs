// PointCloudProcessor.cs
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;

namespace PointCloudSegmentation
{
    /// <summary>
    /// 负责处理点云数据，包括加载、分区和文件写入。
    /// </summary>
    public static class PointCloudProcessor
    {
        /// <summary>
        /// 从指定的 .txt 文件中读取点云数据。
        /// </summary>
        public static List<Point3D> ReadPointCloudFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"文件未找到: {filePath}");
            }

            var points = new List<Point3D>();
            var lines = File.ReadAllLines(filePath);

            foreach (var line in lines)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                var parts = line.Split(new[] { ' ', ',', '\t' }, System.StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 3)
                {
                    if (double.TryParse(parts[0], NumberStyles.Any, CultureInfo.InvariantCulture, out double x) &&
                        double.TryParse(parts[1], NumberStyles.Any, CultureInfo.InvariantCulture, out double y) &&
                        double.TryParse(parts[2], NumberStyles.Any, CultureInfo.InvariantCulture, out double z))
                    {
                        points.Add(new Point3D { X = x, Y = y, Z = z });
                    }
                }
            }
            return points;
        }

        /// <summary>
        /// 计算点云的二维边界框。
        /// </summary>
        public static BoundingBox CalculateBoundingBox(List<Point3D> points)
        {
            if (points == null || points.Count == 0)
            {
                return new BoundingBox { MinX = 0, MaxX = 0, MinY = 0, MaxY = 0 };
            }

            double minX = points.Min(p => p.X);
            double maxX = points.Max(p => p.X);
            double minY = points.Min(p => p.Y);
            double maxY = points.Max(p => p.Y);

            return new BoundingBox { MinX = minX, MaxX = maxX, MinY = minY, MaxY = maxY };
        }

        /// <summary>
        /// 将点分配到十一个预定义的区域。
        /// </summary>
        public static Dictionary<string, Region> PartitionPoints(List<Point3D> points, BoundingBox bbox)
        {
            var regions = new Dictionary<string, Region>
            {
                // 外围4个C区
                { "C1", new Region { Name = "C1" } },
                { "C2", new Region { Name = "C2" } },
                { "C3", new Region { Name = "C3" } },
                { "C4", new Region { Name = "C4" } },
                // 中心小矩形4个A区
                { "A1", new Region { Name = "A1" } },
                { "A2", new Region { Name = "A2" } },
                { "A3", new Region { Name = "A3" } },
                { "A4", new Region { Name = "A4" } },
                // 回字形4个B区
                { "B1", new Region { Name = "B1" } },
                { "B2", new Region { Name = "B2" } },
                { "B3", new Region { Name = "B3" } },
                { "B4", new Region { Name = "B4" } }
            };

            // X轴分割点：1/6 和 5/6
            double x_split1 = bbox.MinX + bbox.Width / 6.0;
            double x_split2 = bbox.MinX + bbox.Width * 5.0 / 6.0;
            double midY = bbox.MinY + bbox.Height / 2.0;

            // 中间区域的边界（原Region_2和Region_5的合并区域）
            double middle_left = x_split1;
            double middle_right = x_split2;
            double middle_top = bbox.MaxY;
            double middle_bottom = bbox.MinY;
            double middle_width = middle_right - middle_left;
            double middle_height = middle_top - middle_bottom;

            // 中心小矩形的边界（长宽都是中间区域的1/2）
            double center_width = middle_width / 2.0;
            double center_height = middle_height / 2.0;
            double center_left = middle_left + (middle_width - center_width) / 2.0;
            double center_right = center_left + center_width;
            double center_bottom = middle_bottom + (middle_height - center_height) / 2.0;
            double center_top = center_bottom + center_height;
            double center_midX = (center_left + center_right) / 2.0;
            double center_midY = (center_bottom + center_top) / 2.0;

            foreach (var point in points)
            {
                // 首先判断是否在外围C区
                if (point.X < x_split1) // 左侧
                {
                    if (point.Y >= midY) regions["C4"].Points.Add(point); // 原1区 → C4（因为Y轴翻转）
                    else regions["C1"].Points.Add(point); // 原4区 → C1（因为Y轴翻转）
                }
                else if (point.X >= x_split2) // 右侧
                {
                    if (point.Y >= midY) regions["C2"].Points.Add(point); // 原3区 → C2（因为Y轴翻转）
                    else regions["C3"].Points.Add(point); // 原6区 → C3（因为Y轴翻转）
                }
                else // 中间区域
                {
                    // 判断是否在中心小矩形内
                    if (point.X >= center_left && point.X < center_right &&
                        point.Y >= center_bottom && point.Y < center_top)
                    {
                        // 中心小矩形的4个A区（因为Y轴翻转，上下对调）
                        if (point.Y >= center_midY) // 上半部分 → 实际显示在下半部分
                        {
                            if (point.X < center_midX) regions["A4"].Points.Add(point); // 左上 → A4（左下）
                            else regions["A3"].Points.Add(point); // 右上 → A3（右下）
                        }
                        else // 下半部分 → 实际显示在上半部分
                        {
                            if (point.X < center_midX) regions["A1"].Points.Add(point); // 左下 → A1（左上）
                            else regions["A2"].Points.Add(point); // 右下 → A2（右上）
                        }
                    }
                    else
                    {
                        // 回字形的4个B区（因为Y轴翻转，上下对调）
                        if (point.Y >= midY) // 上半部分 → 实际显示在下半部分
                        {
                            if (point.X < center_midX) regions["B4"].Points.Add(point); // 左上 → B4（左下）
                            else regions["B3"].Points.Add(point); // 右上 → B3（右下）
                        }
                        else // 下半部分 → 实际显示在上半部分
                        {
                            if (point.X < center_midX) regions["B1"].Points.Add(point); // 左下 → B1（左上）
                            else regions["B2"].Points.Add(point); // 右下 → B2（右上）
                        }
                    }
                }
            }

            return regions;
        }

        /// <summary>
        /// 将分区后的数据写入单独的 .txt 文件。
        /// </summary>
        public static void WriteRegionFiles(Dictionary<string, Region> regions, string outputDir)
        {
            Directory.CreateDirectory(outputDir);
            foreach (var region in regions.Values)
            {
                if (region.Points.Any())
                {
                    var filePath = Path.Combine(outputDir, $"{region.Name}.txt");
                    var lines = region.Points.Select(p => $"{p.X.ToString(CultureInfo.InvariantCulture)} {p.Y.ToString(CultureInfo.InvariantCulture)} {p.Z.ToString(CultureInfo.InvariantCulture)}");
                    File.WriteAllLines(filePath, lines);
                }
            }
        }
    }
}