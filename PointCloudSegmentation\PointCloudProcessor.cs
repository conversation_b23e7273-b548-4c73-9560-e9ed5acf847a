// PointCloudProcessor.cs
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;

namespace PointCloudSegmentation
{
    /// <summary>
    /// 负责处理点云数据，包括加载、分区和文件写入。
    /// </summary>
    public static class PointCloudProcessor
    {
        /// <summary>
        /// 从指定的 .txt 文件中读取点云数据。
        /// </summary>
        public static List<Point3D> ReadPointCloudFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"文件未找到: {filePath}");
            }

            var points = new List<Point3D>();
            var lines = File.ReadAllLines(filePath);

            foreach (var line in lines)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                var parts = line.Split(new[] { ' ', ',', '\t' }, System.StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 3)
                {
                    if (double.TryParse(parts[0], NumberStyles.Any, CultureInfo.InvariantCulture, out double x) &&
                        double.TryParse(parts[1], NumberStyles.Any, CultureInfo.InvariantCulture, out double y) &&
                        double.TryParse(parts[2], NumberStyles.Any, CultureInfo.InvariantCulture, out double z))
                    {
                        points.Add(new Point3D { X = x, Y = y, Z = z });
                    }
                }
            }
            return points;
        }

        /// <summary>
        /// 计算点云的二维边界框。
        /// </summary>
        public static BoundingBox CalculateBoundingBox(List<Point3D> points)
        {
            if (points == null || points.Count == 0)
            {
                return new BoundingBox { MinX = 0, MaxX = 0, MinY = 0, MaxY = 0 };
            }

            double minX = points.Min(p => p.X);
            double maxX = points.Max(p => p.X);
            double minY = points.Min(p => p.Y);
            double maxY = points.Max(p => p.Y);

            return new BoundingBox { MinX = minX, MaxX = maxX, MinY = minY, MaxY = maxY };
        }

        /// <summary>
        /// 将点分配到六个预定义的区域。
        /// </summary>
        public static Dictionary<string, Region> PartitionPoints(List<Point3D> points, BoundingBox bbox)
        {
            var regions = new Dictionary<string, Region>
            {
                { "Region_1", new Region { Name = "Region_1" } },
                { "Region_2", new Region { Name = "Region_2" } },
                { "Region_3", new Region { Name = "Region_3" } },
                { "Region_4", new Region { Name = "Region_4" } },
                { "Region_5", new Region { Name = "Region_5" } },
                { "Region_6", new Region { Name = "Region_6" } }
            };

            // The X-axis is partitioned into 1/6, 1/2 (3/6), and 1/6 ratios.
            double x_split1 = bbox.MinX + bbox.Width / 6.0;
            double x_split2 = bbox.MinX + bbox.Width * 4.0 / 6.0; // This is 1/6 + 3/6
            double midY = bbox.MinY + bbox.Height / 2.0;

            foreach (var point in points)
            {
                if (point.Y >= midY) // Top half
                {
                    if (point.X < x_split1) regions["Region_1"].Points.Add(point);
                    else if (point.X < x_split2) regions["Region_2"].Points.Add(point);
                    else regions["Region_3"].Points.Add(point);
                }
                else // Bottom half
                {
                    if (point.X < x_split1) regions["Region_4"].Points.Add(point);
                    else if (point.X < x_split2) regions["Region_5"].Points.Add(point);
                    else regions["Region_6"].Points.Add(point);
                }
            }

            return regions;
        }

        /// <summary>
        /// 将分区后的数据写入单独的 .txt 文件。
        /// </summary>
        public static void WriteRegionFiles(Dictionary<string, Region> regions, string outputDir)
        {
            Directory.CreateDirectory(outputDir);
            foreach (var region in regions.Values)
            {
                if (region.Points.Any())
                {
                    var filePath = Path.Combine(outputDir, $"{region.Name}.txt");
                    var lines = region.Points.Select(p => $"{p.X.ToString(CultureInfo.InvariantCulture)} {p.Y.ToString(CultureInfo.InvariantCulture)} {p.Z.ToString(CultureInfo.InvariantCulture)}");
                    File.WriteAllLines(filePath, lines);
                }
            }
        }
    }
}