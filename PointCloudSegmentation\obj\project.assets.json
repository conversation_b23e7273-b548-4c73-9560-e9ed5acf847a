{"version": 3, "targets": {"net9.0": {"SixLabors.Fonts/2.1.3": {"type": "package", "compile": {"lib/net6.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/3.1.10": {"type": "package", "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "build": {"build/SixLabors.ImageSharp.props": {}}}, "SixLabors.ImageSharp.Drawing/2.1.6": {"type": "package", "dependencies": {"SixLabors.Fonts": "2.1.3", "SixLabors.ImageSharp": "3.1.8"}, "compile": {"lib/net6.0/SixLabors.ImageSharp.Drawing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.Drawing.dll": {"related": ".xml"}}}}}, "libraries": {"SixLabors.Fonts/2.1.3": {"sha512": "ORWbZ5BHrC/LZvo+Y09MnoJq5VUKD85LsYALk+YI7CHFra+m5arCkz00IntDM6SrAiB22bvSdKtKmuCyHOKlqg==", "type": "package", "path": "sixlabors.fonts/2.1.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net6.0/SixLabors.Fonts.dll", "lib/net6.0/SixLabors.Fonts.xml", "sixlabors.fonts.128.png", "sixlabors.fonts.2.1.3.nupkg.sha512", "sixlabors.fonts.nuspec"]}, "SixLabors.ImageSharp/3.1.10": {"sha512": "R1HEPcqx3v+kvlOTPouP0g/Nzzud9pHtjlgGbFax3Ivaz8kkaGfS2EPfyDGpmfoTUQ3nQ5wxdhYyYa9fwYA9cw==", "type": "package", "path": "sixlabors.imagesharp/3.1.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "build/SixLabors.ImageSharp.props", "lib/net6.0/SixLabors.ImageSharp.dll", "lib/net6.0/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.3.1.10.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "SixLabors.ImageSharp.Drawing/2.1.6": {"sha512": "/VeIBfAcaYcqqWMgVyF0nzdmWBkDQhppL8tltd8pFyUYanytCFnJG77bZYOGllk4LYLFdKduACvbpKGUpflp1w==", "type": "package", "path": "sixlabors.imagesharp.drawing/2.1.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net6.0/SixLabors.ImageSharp.Drawing.dll", "lib/net6.0/SixLabors.ImageSharp.Drawing.xml", "sixlabors.imagesharp.drawing.128.png", "sixlabors.imagesharp.drawing.2.1.6.nupkg.sha512", "sixlabors.imagesharp.drawing.nuspec"]}}, "projectFileDependencyGroups": {"net9.0": ["SixLabors.ImageSharp >= 3.1.10", "SixLabors.ImageSharp.Drawing >= 2.1.6"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\csharp\\xyz_analyze\\PointCloudSegmentation\\PointCloudSegmentation.csproj", "projectName": "PointCloudSegmentation", "projectPath": "D:\\csharp\\xyz_analyze\\PointCloudSegmentation\\PointCloudSegmentation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\csharp\\xyz_analyze\\PointCloudSegmentation\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.10, )"}, "SixLabors.ImageSharp.Drawing": {"target": "Package", "version": "[2.1.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}}}}