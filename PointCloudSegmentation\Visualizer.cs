// Visualizer.cs
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.PixelFormats;
using System.Collections.Generic;
using System.Linq;
using SixLabors.Fonts;
using SixLabors.ImageSharp.Drawing;

namespace PointCloudSegmentation
{
    /// <summary>
    /// 负责生成点云分区的可视化图像。
    /// </summary>
    public static class Visualizer
    {
        /// <summary>
        /// 生成并保存一个 PNG 图像，显示分区和点。
        /// </summary>
        public static void GenerateVisualization(Dictionary<string, Region> regions, BoundingBox bbox, string outputPath)
        {
            int imageWidth = 1024;
            int imageHeight = 1024;
            int padding = 50;

            using (var image = new Image<Rgba32>(imageWidth, imageHeight))
            {
                image.Mutate(ctx =>
                {
                    ctx.Fill(Color.White);

                    if (!regions.Any(r => r.Value.Points.Any()))
                    {
                        // Handle empty point cloud case
                        return;
                    }

                    var colorMap = new Dictionary<string, Color>
                    {
                        // 外围C区
                        { "C1", Color.Red },
                        { "C2", Color.Blue },
                        { "C3", Color.Green },
                        { "C4", Color.Yellow },
                        // 中心A区
                        { "A1", Color.Purple },
                        { "A2", Color.Orange },
                        { "A3", Color.Pink },
                        { "A4", Color.Brown },
                        // 回字形B区
                        { "B1", Color.Cyan },
                        { "B2", Color.Magenta },
                        { "B3", Color.Gray },
                        { "B4", Color.DarkGreen }
                    };

                    double scaleX = (imageWidth - 2 * padding) / bbox.Width;
                    double scaleY = (imageHeight - 2 * padding) / bbox.Height;
                    double scale = System.Math.Min(scaleX, scaleY);
                    
                    Font font = SystemFonts.CreateFont("Arial", 12, FontStyle.Regular);
                    Font cornerFont = SystemFonts.CreateFont("Arial", 10, FontStyle.Regular);

                    foreach (var region in regions.Values)
                    {
                        if (!region.Points.Any()) continue;

                        var color = colorMap.ContainsKey(region.Name) ? colorMap[region.Name] : Color.Gray;

                        foreach (var point in region.Points)
                        {
                            float px = (float)((point.X - bbox.MinX) * scale) + padding;
                            float py = (float)((point.Y - bbox.MinY) * scale) + padding;
                            ctx.Fill(color, new EllipsePolygon(px, py, 2));
                        }
                        
                        // Calculate region center for text label
                        if (region.Points.Any())
                        {
                            var regionBbox = PointCloudProcessor.CalculateBoundingBox(region.Points);
                            float centerX = (float)((regionBbox.CenterX - bbox.MinX) * scale) + padding;
                            float centerY = (float)((regionBbox.CenterY - bbox.MinY) * scale) + padding;
                            
                            string regionId = region.Name; // 直接使用区域名称，如C1, A1, B1等
                            var textSize = TextMeasurer.MeasureSize(regionId, new TextOptions(font));
                            ctx.DrawText(regionId, font, Color.Black, new PointF(centerX - textSize.Width / 2, centerY - textSize.Height / 2));
                        }
                    }

                    // Define screen coordinates for drawing
                    float imgMinX = padding;
                    float imgMaxX = (float)(bbox.Width * scale) + padding;
                    float imgMinY = padding;
                    float imgMaxY = (float)(bbox.Height * scale) + padding;

                    // Draw bounding box
                    ctx.Draw(Pens.Solid(Color.Black, 2), new RectangleF(imgMinX, imgMinY, imgMaxX - imgMinX, imgMaxY - imgMinY));

                    // Draw partition lines
                    float screen_x1 = (float)((bbox.Width / 6.0) * scale) + padding;
                    float screen_x2 = (float)((bbox.Width * 5.0 / 6.0) * scale) + padding;
                    float midY_screen = (float)((bbox.Height / 2.0) * scale) + padding;

                    // 主要分割线（外围C区的分割线）
                    ctx.DrawLine(Color.Black, 2, new PointF(screen_x1, imgMinY), new PointF(screen_x1, imgMaxY));
                    ctx.DrawLine(Color.Black, 2, new PointF(screen_x2, imgMinY), new PointF(screen_x2, imgMaxY));
                    ctx.DrawLine(Color.Black, 2, new PointF(imgMinX, midY_screen), new PointF(imgMaxX, midY_screen));

                    // 计算中间区域的屏幕坐标
                    float middle_width = screen_x2 - screen_x1;
                    float middle_height = imgMaxY - imgMinY;

                    // 中心小矩形的屏幕坐标
                    float center_width = middle_width / 2.0f;
                    float center_height = middle_height / 2.0f;
                    float center_left = screen_x1 + (middle_width - center_width) / 2.0f;
                    float center_right = center_left + center_width;
                    float center_bottom = imgMinY + (middle_height - center_height) / 2.0f;
                    float center_top = center_bottom + center_height;
                    float center_midX = (center_left + center_right) / 2.0f;
                    float center_midY = (center_bottom + center_top) / 2.0f;

                    // 绘制中心小矩形
                    ctx.Draw(Pens.Solid(Color.Red, 2), new RectangleF(center_left, center_bottom, center_width, center_height));

                    // 绘制中心小矩形的分割线（A区分割线）
                    ctx.DrawLine(Color.Red, 1, new PointF(center_midX, center_bottom), new PointF(center_midX, center_top));
                    ctx.DrawLine(Color.Red, 1, new PointF(center_left, center_midY), new PointF(center_right, center_midY));

                    // 绘制回字形区域的分割线（B区分割线）
                    ctx.DrawLine(Color.Blue, 1, new PointF(center_midX, imgMinY), new PointF(center_midX, center_bottom));
                    ctx.DrawLine(Color.Blue, 1, new PointF(center_midX, center_top), new PointF(center_midX, imgMaxY));
                    ctx.DrawLine(Color.Blue, 1, new PointF(screen_x1, center_midY), new PointF(center_left, center_midY));
                    ctx.DrawLine(Color.Blue, 1, new PointF(center_right, center_midY), new PointF(screen_x2, center_midY));

                    // Draw corner coordinates outside the box
                    // Draw corner coordinates outside the box with proper alignment
                    var textOptionsTopLeft = new TextOptions(cornerFont) { HorizontalAlignment = HorizontalAlignment.Right, VerticalAlignment = VerticalAlignment.Bottom };
                    var textOptionsTopRight = new TextOptions(cornerFont) { HorizontalAlignment = HorizontalAlignment.Left, VerticalAlignment = VerticalAlignment.Bottom };
                    var textOptionsBottomLeft = new TextOptions(cornerFont) { HorizontalAlignment = HorizontalAlignment.Right, VerticalAlignment = VerticalAlignment.Top };
                    var textOptionsBottomRight = new TextOptions(cornerFont) { HorizontalAlignment = HorizontalAlignment.Left, VerticalAlignment = VerticalAlignment.Top };

                    ctx.DrawText($"( {bbox.MinX:F2} , {bbox.MinY:F2} )", cornerFont, Color.DarkSlateGray, new PointF(imgMinX - 5, imgMinY - 5));
ctx.DrawText($"( {bbox.MaxX:F2} , {bbox.MinY:F2} )", cornerFont, Color.DarkSlateGray, new PointF(imgMaxX + 5, imgMinY - 5));
ctx.DrawText($"( {bbox.MinX:F2} , {bbox.MaxY:F2} )", cornerFont, Color.DarkSlateGray, new PointF(imgMinX - 5, imgMaxY + 5));
ctx.DrawText($"( {bbox.MaxX:F2} , {bbox.MaxY:F2} )", cornerFont, Color.DarkSlateGray, new PointF(imgMaxX + 5, imgMaxY + 5));
                });

                image.Save(outputPath);
            }
        }
    }
}