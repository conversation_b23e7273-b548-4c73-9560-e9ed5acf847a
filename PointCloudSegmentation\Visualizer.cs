// Visualizer.cs
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.PixelFormats;
using System.Collections.Generic;
using System.Linq;
using SixLabors.Fonts;
using SixLabors.ImageSharp.Drawing;

namespace PointCloudSegmentation
{
    /// <summary>
    /// 负责生成点云分区的可视化图像。
    /// </summary>
    public static class Visualizer
    {
        /// <summary>
        /// 生成并保存一个 PNG 图像，显示分区和点。
        /// </summary>
        public static void GenerateVisualization(Dictionary<string, Region> regions, BoundingBox bbox, string outputPath)
        {
            int imageWidth = 1024;
            int imageHeight = 1024;
            int padding = 50;

            using (var image = new Image<Rgba32>(imageWidth, imageHeight))
            {
                image.Mutate(ctx =>
                {
                    ctx.Fill(Color.White);

                    if (!regions.Any(r => r.Value.Points.Any()))
                    {
                        // Handle empty point cloud case
                        return;
                    }

                    var colorMap = new Dictionary<string, Color>
                    {
                        { "Region_1", Color.Red },
                        { "Region_2", Color.Green },
                        { "Region_3", Color.Blue },
                        { "Region_4", Color.Yellow },
                        { "Region_5", Color.Magenta },
                        { "Region_6", Color.Cyan }
                    };

                    double scaleX = (imageWidth - 2 * padding) / bbox.Width;
                    double scaleY = (imageHeight - 2 * padding) / bbox.Height;
                    double scale = System.Math.Min(scaleX, scaleY);
                    
                    Font font = SystemFonts.CreateFont("Arial", 12, FontStyle.Regular);
                    Font cornerFont = SystemFonts.CreateFont("Arial", 10, FontStyle.Regular);

                    foreach (var region in regions.Values)
                    {
                        if (!region.Points.Any()) continue;

                        var color = colorMap.ContainsKey(region.Name) ? colorMap[region.Name] : Color.Gray;

                        foreach (var point in region.Points)
                        {
                            float px = (float)((point.X - bbox.MinX) * scale) + padding;
                            float py = (float)((point.Y - bbox.MinY) * scale) + padding;
                            ctx.Fill(color, new EllipsePolygon(px, py, 2));
                        }
                        
                        // Calculate region center for text label
                        if (region.Points.Any())
                        {
                            var regionBbox = PointCloudProcessor.CalculateBoundingBox(region.Points);
                            float centerX = (float)((regionBbox.CenterX - bbox.MinX) * scale) + padding;
                            float centerY = (float)((regionBbox.CenterY - bbox.MinY) * scale) + padding;
                            
                            string regionId = region.Name.Replace("Region_", "");
                            var textSize = TextMeasurer.MeasureSize(regionId, new TextOptions(font));
                            ctx.DrawText(regionId, font, Color.Black, new PointF(centerX - textSize.Width / 2, centerY - textSize.Height / 2));
                        }
                    }

                    // Define screen coordinates for drawing
                    float imgMinX = padding;
                    float imgMaxX = (float)(bbox.Width * scale) + padding;
                    float imgMinY = padding;
                    float imgMaxY = (float)(bbox.Height * scale) + padding;

                    // Draw bounding box
                    ctx.Draw(Pens.Solid(Color.Black, 2), new RectangleF(imgMinX, imgMinY, imgMaxX - imgMinX, imgMaxY - imgMinY));

                    // Draw partition lines
                    float screen_x1 = (float)((bbox.MinX + bbox.Width / 6.0 - bbox.MinX) * scale) + padding;
                    float screen_x2 = (float)((bbox.MinX + bbox.Width * 4.0 / 6.0 - bbox.MinX) * scale) + padding;
                    float midY_screen = (float)((bbox.MinY + bbox.Height / 2.0 - bbox.MinY) * scale) + padding;

                    ctx.DrawLine(Color.DarkGray, 1, new PointF(screen_x1, imgMinY), new PointF(screen_x1, imgMaxY));
                    ctx.DrawLine(Color.DarkGray, 1, new PointF(screen_x2, imgMinY), new PointF(screen_x2, imgMaxY));
                    ctx.DrawLine(Color.DarkGray, 1, new PointF(imgMinX, midY_screen), new PointF(imgMaxX, midY_screen));

                    // Draw corner coordinates outside the box
                    // Draw corner coordinates outside the box
                    var topLeftTextOptions = new TextOptions(cornerFont) { HorizontalAlignment = HorizontalAlignment.Right, VerticalAlignment = VerticalAlignment.Bottom, Origin = new PointF(-5, -5) };
                    var topRightTextOptions = new TextOptions(cornerFont) { HorizontalAlignment = HorizontalAlignment.Left, VerticalAlignment = VerticalAlignment.Bottom, Origin = new PointF(5, -5) };
                    var bottomLeftTextOptions = new TextOptions(cornerFont) { HorizontalAlignment = HorizontalAlignment.Right, VerticalAlignment = VerticalAlignment.Top, Origin = new PointF(-5, 5) };
                    var bottomRightTextOptions = new TextOptions(cornerFont) { HorizontalAlignment = HorizontalAlignment.Left, VerticalAlignment = VerticalAlignment.Top, Origin = new PointF(5, 5) };

                    ctx.DrawText($"( {bbox.MinX:F2} , {bbox.MinY:F2} )", cornerFont, Color.DarkSlateGray, new PointF(imgMinX, imgMinY), topLeftTextOptions);
                    ctx.DrawText($"( {bbox.MaxX:F2} , {bbox.MinY:F2} )", cornerFont, Color.DarkSlateGray, new PointF(imgMaxX, imgMinY), topRightTextOptions);
                    ctx.DrawText($"( {bbox.MinX:F2} , {bbox.MaxY:F2} )", cornerFont, Color.DarkSlateGray, new PointF(imgMinX, imgMaxY), bottomLeftTextOptions);
                    ctx.DrawText($"( {bbox.MaxX:F2} , {bbox.MaxY:F2} )", cornerFont, Color.DarkSlateGray, new PointF(imgMaxX, imgMaxY), bottomRightTextOptions);
                });

                image.Save(outputPath);
            }
        }
    }
}