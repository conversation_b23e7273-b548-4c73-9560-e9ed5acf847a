# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-07-02 16:32:27 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   Build a C# command-line tool to read, parse, and segment point cloud data from a text file into six predefined geographical regions.

## Key Features

*   Load 3D point data from a .txt file.
*   Calculate the 2D bounding box of the point cloud.
*   Partition points into six regions based on X and Y coordinates.
*   Generate separate .txt files for each region containing points.
*   Create a 2D top-down visualization of the partitioned regions as a PNG image.
*   Handle file-not-found, format, and empty data errors.

## Overall Architecture

*   A C# console application.