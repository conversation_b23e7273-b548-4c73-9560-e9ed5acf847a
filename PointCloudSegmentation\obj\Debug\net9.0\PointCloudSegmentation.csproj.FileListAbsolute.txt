D:\csharp\xyz_analyze\PointCloudSegmentation\bin\Debug\net9.0\PointCloudSegmentation.exe
D:\csharp\xyz_analyze\PointCloudSegmentation\bin\Debug\net9.0\PointCloudSegmentation.deps.json
D:\csharp\xyz_analyze\PointCloudSegmentation\bin\Debug\net9.0\PointCloudSegmentation.runtimeconfig.json
D:\csharp\xyz_analyze\PointCloudSegmentation\bin\Debug\net9.0\PointCloudSegmentation.dll
D:\csharp\xyz_analyze\PointCloudSegmentation\bin\Debug\net9.0\PointCloudSegmentation.pdb
D:\csharp\xyz_analyze\PointCloudSegmentation\bin\Debug\net9.0\SixLabors.Fonts.dll
D:\csharp\xyz_analyze\PointCloudSegmentation\bin\Debug\net9.0\SixLabors.ImageSharp.dll
D:\csharp\xyz_analyze\PointCloudSegmentation\bin\Debug\net9.0\SixLabors.ImageSharp.Drawing.dll
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\PointCloudSegmentation.csproj.AssemblyReference.cache
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\PointCloudSegmentation.GeneratedMSBuildEditorConfig.editorconfig
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\PointCloudSegmentation.AssemblyInfoInputs.cache
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\PointCloudSegmentation.AssemblyInfo.cs
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\PointCloudSegmentation.csproj.CoreCompileInputs.cache
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\PointClo.EB280DD8.Up2Date
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\PointCloudSegmentation.dll
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\refint\PointCloudSegmentation.dll
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\PointCloudSegmentation.pdb
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\PointCloudSegmentation.genruntimeconfig.cache
D:\csharp\xyz_analyze\PointCloudSegmentation\obj\Debug\net9.0\ref\PointCloudSegmentation.dll
