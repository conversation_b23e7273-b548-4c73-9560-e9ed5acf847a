// PointCloudModels.cs
namespace PointCloudSegmentation
{
    /// <summary>
    /// 表示一个三维空间中的点。
    /// </summary>
    public struct Point3D
    {
        public double X { get; set; }
        public double Y { get; set; }
        public double Z { get; set; }
    }

    /// <summary>
    /// 定义点云在 X-Y 平面上的二维最小外接矩形。
    /// </summary>
    public class BoundingBox
    {
        public double MinX { get; set; }
        public double MaxX { get; set; }
        public double MinY { get; set; }
        public double MaxY { get; set; }
        public double Width => MaxX - MinX;
        public double Height => MaxY - MinY;
        public double CenterX => MinX + Width / 2.0;
        public double CenterY => MinY + Height / 2.0;
    }

    /// <summary>
    /// 表示一个地理分区，包含其名称、边界和点集。
    /// </summary>
    public class Region
    {
        public string Name { get; set; }
        public List<Point3D> Points { get; } = new List<Point3D>();
    }
}